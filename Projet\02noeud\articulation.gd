extends Node2D

var ProjectileScene := preload("res://Projectile.tscn")

@export var vitesse_epaule: float = 2.
@export var vitesse_bras: float = 3.

@onready var epaule_droite = $"Corp/epaule droit"
@onready var bras_droit = $"Corp/epaule droit/bras droit"





func _ready() -> void:
	epaule_droite.self_modulate = Color.LIGHT_PINK
	bras_droit.self_modulate = Color.LIGHT_BLUE

func _process(delta: float) -> void:
	if Input.is_action_pressed("ui_left"):
		epaule_droite.rotation -= vitesse_epaule * delta
	if Input.is_action_pressed("ui_right"):
		epaule_droite.rotation += vitesse_epaule * delta

	if Input.is_action_pressed("ui_up"):
		bras_droit.rotation -= vitesse_bras * delta
	if Input.is_action_pressed("ui_down"):
		bras_droit.rotation += vitesse_bras * delta
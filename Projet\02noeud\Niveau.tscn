[gd_scene load_steps=30 format=3 uid="uid://prmchej6rrno"]

[ext_resource type="Texture2D" uid="uid://bkwj4vyp0o1uw" path="res://Foret/bg_color.png" id="1_4k40v"]
[ext_resource type="Script" uid="uid://cemgk4ww73npp" path="res://paysage.gd" id="1_8f2nu"]
[ext_resource type="Texture2D" uid="uid://dpdflmr1g3uni" path="res://Foret/bg_tree3.png" id="2_8f2nu"]
[ext_resource type="Texture2D" uid="uid://d2xaf3hrxg015" path="res://Foret/bg_tree4.png" id="3_qeu34"]
[ext_resource type="Texture2D" uid="uid://jjqih8r4dhw2" path="res://Foret/ground2.png" id="4_d3ejn"]
[ext_resource type="Texture2D" uid="uid://bdn0nohv6nvkg" path="res://Foret/ground1.png" id="5_2820u"]
[ext_resource type="Texture2D" uid="uid://clefimr5y1rb0" path="res://Foret/bush3.png" id="6_2y3gk"]
[ext_resource type="Texture2D" uid="uid://c2jig3mljp1u" path="res://Foret/ground6.png" id="7_hyav3"]
[ext_resource type="Texture2D" uid="uid://dkalcsybx3fqa" path="res://Foret/ground4.png" id="8_1am4y"]
[ext_resource type="Texture2D" uid="uid://bqwvg570d3628" path="res://Foret/ground5.png" id="9_tw014"]
[ext_resource type="Texture2D" uid="uid://hb8hsem3tqmr" path="res://Foret/ground9.png" id="10_fe3s8"]
[ext_resource type="Texture2D" uid="uid://d3dacyx8eoful" path="res://Foret/ground8.png" id="11_xbqou"]
[ext_resource type="Texture2D" uid="uid://cmnlxvn6ny0s1" path="res://Foret/treehouse.png" id="12_tyc8b"]
[ext_resource type="Texture2D" uid="uid://degc82wbtfhgo" path="res://Foret/bg_tree6.png" id="13_oow8v"]
[ext_resource type="Texture2D" uid="uid://cbdxt5hbsuftb" path="res://Foret/stone6.png" id="14_i3c2l"]
[ext_resource type="Texture2D" uid="uid://bfahjmye6ivoe" path="res://Foret/stone5.png" id="15_b741t"]
[ext_resource type="Texture2D" uid="uid://dkvjn57ob1lmi" path="res://Foret/tree3.png" id="16_jq4hi"]
[ext_resource type="Texture2D" uid="uid://cfme4ae1nxqfu" path="res://Foret/top2.png" id="17_yv6i5"]
[ext_resource type="Texture2D" uid="uid://dip1kqmjougfx" path="res://Foret/board2.png" id="18_uej03"]
[ext_resource type="Texture2D" uid="uid://nb2p3ecogsct" path="res://Foret/tree5.png" id="19_3vjd0"]
[ext_resource type="Texture2D" uid="uid://lphrv05wk4ru" path="res://Foret/bush2.png" id="20_r8iuw"]
[ext_resource type="Texture2D" uid="uid://da75kqd4t1ajm" path="res://Foret/bush1.png" id="21_8kwux"]
[ext_resource type="Texture2D" uid="uid://ccuynglchnt4o" path="res://Foret/stone3.png" id="22_gtyq2"]
[ext_resource type="Texture2D" uid="uid://dyymacch6cyy2" path="res://Foret/ruin1.png" id="23_l3s5c"]
[ext_resource type="Texture2D" uid="uid://6fkmvo0eotu5" path="res://Foret/stone7.png" id="24_83hx3"]
[ext_resource type="Texture2D" uid="uid://ci30pp3i75ho0" path="res://Foret/bg_tree9.png" id="25_selwa"]
[ext_resource type="PackedScene" uid="uid://2y26hb7n44jo" path="res://gazon.tscn" id="26_2qyop"]
[ext_resource type="PackedScene" uid="uid://b751r03r8xowx" path="res://Projectile.tscn" id="27_2qyop"]
[ext_resource type="PackedScene" uid="uid://dbtiid3jqxfis" path="res://articulation.tscn" id="28_qeu34"]

[node name="Node2D" type="Node2D"]

[node name="Paysage" type="Node2D" parent="."]
script = ExtResource("1_8f2nu")

[node name="BgColor" type="Sprite2D" parent="Paysage"]
position = Vector2(851, 506)
scale = Vector2(17.8509, 7.4574)
texture = ExtResource("1_4k40v")

[node name="BgTree3" type="Sprite2D" parent="Paysage"]
position = Vector2(145, 392.75)
scale = Vector2(1.125, 1.09644)
texture = ExtResource("2_8f2nu")

[node name="BgTree4" type="Sprite2D" parent="Paysage"]
position = Vector2(350, 294)
scale = Vector2(1.29327, 1.29327)
texture = ExtResource("3_qeu34")

[node name="Ground2" type="Sprite2D" parent="Paysage"]
position = Vector2(308, 647)
texture = ExtResource("4_d3ejn")

[node name="Ground1" type="Sprite2D" parent="Paysage"]
position = Vector2(102, 646)
texture = ExtResource("5_2820u")

[node name="Bush3" type="Sprite2D" parent="Paysage"]
position = Vector2(54, 549)
texture = ExtResource("6_2y3gk")

[node name="Bush4" type="Sprite2D" parent="Paysage"]
position = Vector2(144, 551)
texture = ExtResource("6_2y3gk")

[node name="Bush5" type="Sprite2D" parent="Paysage"]
position = Vector2(54, 549)
texture = ExtResource("6_2y3gk")

[node name="Bush6" type="Sprite2D" parent="Paysage"]
position = Vector2(239, 574)
texture = ExtResource("6_2y3gk")

[node name="Ground15" type="Sprite2D" parent="Paysage"]
position = Vector2(327, 489)
texture = ExtResource("7_hyav3")

[node name="Ground6" type="Sprite2D" parent="Paysage"]
position = Vector2(373, 575)
scale = Vector2(0.652439, 0.652439)
texture = ExtResource("8_1am4y")

[node name="Ground4" type="Sprite2D" parent="Paysage"]
position = Vector2(839, 669)
texture = ExtResource("8_1am4y")

[node name="Ground5" type="Sprite2D" parent="Paysage"]
position = Vector2(851, 584)
scale = Vector2(1.47872, 2.05172)
texture = ExtResource("9_tw014")

[node name="Ground7" type="Sprite2D" parent="Paysage"]
position = Vector2(505, 563)
scale = Vector2(0.652439, 0.652439)
texture = ExtResource("8_1am4y")

[node name="Ground9" type="Sprite2D" parent="Paysage"]
position = Vector2(644, 581)
scale = Vector2(0.652439, 0.652439)
texture = ExtResource("8_1am4y")

[node name="Ground10" type="Sprite2D" parent="Paysage"]
position = Vector2(770, 559)
scale = Vector2(0.652439, 0.652439)
texture = ExtResource("8_1am4y")

[node name="Ground11" type="Sprite2D" parent="Paysage"]
position = Vector2(883, 546)
scale = Vector2(0.652439, 0.652439)
texture = ExtResource("8_1am4y")

[node name="Ground14" type="Sprite2D" parent="Paysage"]
position = Vector2(447, 536)
texture = ExtResource("10_fe3s8")

[node name="Ground12" type="Sprite2D" parent="Paysage"]
position = Vector2(1025, 580)
scale = Vector2(0.652439, 0.652439)
texture = ExtResource("8_1am4y")

[node name="Ground13" type="Sprite2D" parent="Paysage"]
position = Vector2(1006, 518)
scale = Vector2(0.652439, 0.652439)
texture = ExtResource("8_1am4y")

[node name="Ground8" type="Sprite2D" parent="Paysage"]
position = Vector2(563, 627)
texture = ExtResource("11_xbqou")

[node name="Treehouse" type="Sprite2D" parent="Paysage"]
position = Vector2(180.367, 323.5)
scale = Vector2(0.54034, 0.754444)
texture = ExtResource("12_tyc8b")

[node name="BgTree6" type="Sprite2D" parent="Paysage"]
position = Vector2(521, 337)
scale = Vector2(0.625307, 0.574592)
texture = ExtResource("13_oow8v")

[node name="BgTree7" type="Sprite2D" parent="Paysage"]
position = Vector2(607, 328)
scale = Vector2(0.625307, 0.574592)
texture = ExtResource("13_oow8v")

[node name="BgTree8" type="Sprite2D" parent="Paysage"]
position = Vector2(787, 364)
scale = Vector2(0.625307, 0.574592)
texture = ExtResource("13_oow8v")

[node name="BgTree19" type="Sprite2D" parent="Paysage"]
position = Vector2(787, 364)
scale = Vector2(0.625307, 0.574592)
texture = ExtResource("13_oow8v")

[node name="BgTree9" type="Sprite2D" parent="Paysage"]
position = Vector2(849, 343)
scale = Vector2(0.625307, 0.574592)
texture = ExtResource("13_oow8v")

[node name="BgTree10" type="Sprite2D" parent="Paysage"]
position = Vector2(673, 354)
scale = Vector2(0.625307, 0.574592)
texture = ExtResource("13_oow8v")

[node name="BgTree11" type="Sprite2D" parent="Paysage"]
position = Vector2(761, 384)
scale = Vector2(0.625307, 0.574592)
texture = ExtResource("13_oow8v")

[node name="BgTree12" type="Sprite2D" parent="Paysage"]
position = Vector2(705.375, 337.625)
scale = Vector2(0.489558, 0.380536)
texture = ExtResource("13_oow8v")

[node name="BgTree13" type="Sprite2D" parent="Paysage"]
position = Vector2(594, 308)
scale = Vector2(0.489558, 0.380536)
texture = ExtResource("13_oow8v")

[node name="BgTree16" type="Sprite2D" parent="Paysage"]
position = Vector2(903, 365)
scale = Vector2(0.489558, 0.380536)
texture = ExtResource("13_oow8v")

[node name="BgTree17" type="Sprite2D" parent="Paysage"]
position = Vector2(738, 355)
scale = Vector2(0.489558, 0.380536)
texture = ExtResource("13_oow8v")

[node name="BgTree18" type="Sprite2D" parent="Paysage"]
position = Vector2(616.188, 372.813)
scale = Vector2(0.362715, 0.316142)
texture = ExtResource("13_oow8v")

[node name="BgTree14" type="Sprite2D" parent="Paysage"]
position = Vector2(499, 322)
scale = Vector2(0.489558, 0.380536)
texture = ExtResource("13_oow8v")

[node name="BgTree15" type="Sprite2D" parent="Paysage"]
position = Vector2(499, 322)
scale = Vector2(0.489558, 0.380536)
texture = ExtResource("13_oow8v")

[node name="Stone6" type="Sprite2D" parent="Paysage"]
position = Vector2(647, 600)
texture = ExtResource("14_i3c2l")

[node name="Stone5" type="Sprite2D" parent="Paysage"]
position = Vector2(220, 671)
texture = ExtResource("15_b741t")

[node name="Tree3" type="Sprite2D" parent="Paysage"]
position = Vector2(487, 350)
scale = Vector2(1.12638, 1.08043)
texture = ExtResource("16_jq4hi")

[node name="Ground16" type="Sprite2D" parent="Paysage"]
position = Vector2(1045, 623)
texture = ExtResource("10_fe3s8")

[node name="Ground17" type="Sprite2D" parent="Paysage"]
position = Vector2(919, 499)
texture = ExtResource("10_fe3s8")

[node name="Ground18" type="Sprite2D" parent="Paysage"]
position = Vector2(561, 518)
texture = ExtResource("10_fe3s8")

[node name="Ground19" type="Sprite2D" parent="Paysage"]
position = Vector2(786, 555)
texture = ExtResource("10_fe3s8")

[node name="Top2" type="Sprite2D" parent="Paysage"]
position = Vector2(456, 475)
scale = Vector2(0.512784, 0.391844)
texture = ExtResource("17_yv6i5")

[node name="Board2" type="Sprite2D" parent="Paysage"]
position = Vector2(925, 438)
scale = Vector2(0.716867, 0.512987)
texture = ExtResource("18_uej03")

[node name="Tree5" type="Sprite2D" parent="Paysage"]
position = Vector2(731.5, 297.842)
scale = Vector2(1.10088, 1.32671)
texture = ExtResource("19_3vjd0")

[node name="Stone7" type="Sprite2D" parent="Paysage"]
position = Vector2(842, 467)
texture = ExtResource("15_b741t")

[node name="Bush7" type="Sprite2D" parent="Paysage"]
position = Vector2(576, 451)
texture = ExtResource("6_2y3gk")

[node name="Bush2" type="Sprite2D" parent="Paysage"]
position = Vector2(692, 504)
texture = ExtResource("20_r8iuw")

[node name="Bush8" type="Sprite2D" parent="Paysage"]
position = Vector2(937, 548)
texture = ExtResource("21_8kwux")

[node name="Stone3" type="Sprite2D" parent="Paysage"]
position = Vector2(1024, 523)
texture = ExtResource("22_gtyq2")

[node name="Ruin1" type="Sprite2D" parent="Paysage"]
position = Vector2(1080, 524)
scale = Vector2(0.688034, 0.688034)
texture = ExtResource("23_l3s5c")

[node name="Stone8" type="Sprite2D" parent="Paysage"]
position = Vector2(520, 570)
texture = ExtResource("24_83hx3")

[node name="Stone9" type="Sprite2D" parent="Paysage"]
position = Vector2(848, 615)
texture = ExtResource("14_i3c2l")

[node name="Bush1" type="Sprite2D" parent="Paysage"]
position = Vector2(773, 482)
texture = ExtResource("21_8kwux")

[node name="BgTree20" type="Sprite2D" parent="Paysage"]
position = Vector2(957.875, 401.609)
scale = Vector2(0.251493, 0.307966)
texture = ExtResource("25_selwa")

[node name="BgTree21" type="Sprite2D" parent="Paysage"]
position = Vector2(1003, 378)
scale = Vector2(0.251493, 0.307966)
texture = ExtResource("25_selwa")

[node name="Gazon" parent="Paysage" instance=ExtResource("26_2qyop")]
position = Vector2(475, 597)

[node name="Gazon2" parent="Paysage" instance=ExtResource("26_2qyop")]
position = Vector2(817, 538)

[node name="Gazon3" parent="Paysage" instance=ExtResource("26_2qyop")]
position = Vector2(642, 608)

[node name="Projectile" parent="." instance=ExtResource("27_2qyop")]
position = Vector2(85, 532)

[node name="Articulation" parent="." instance=ExtResource("28_qeu34")]
position = Vector2(565, 322)
